#!/bin/bash

# 训练脚本 - TrainDiffusionUnetHybridPointcloudWorkspace
# 使用合并后的官方权重兼容配置文件
# 支持从权重文件恢复训练，完全兼容官方实现
#
# 使用方法:
#   ./train_hybrid_pointcloud.sh
#
# 主要特性:
#   - 完全兼容官方权重 (68.8M参数)
#   - 自动错误检查和路径验证
#   - 详细的训练配置输出
#   - 支持断点续训
#   - 保存top-5最佳模型

echo "=========================================="
echo "🚀 开始训练 - Hybrid Pointcloud Workspace"
echo "=========================================="

# 激活conda环境
source ~/miniconda3/etc/profile.d/conda.sh
conda activate idp3

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0
export WANDB_SILENT=True
export HYDRA_FULL_ERROR=1

# 检查配置文件是否存在
CONFIG_FILE="diffusion_policy_3d/config/train_diffusion_unet_hybrid_pointcloud_workspace.yaml"
if [ ! -f "$CONFIG_FILE" ]; then
    echo "❌ 配置文件不存在: $CONFIG_FILE"
    exit 1
fi

echo "✅ 使用配置文件: $CONFIG_FILE"

# 数据路径配置
DATASET_PATH="/home/<USER>/code/company/torqueidp3/data/raw_pour_converted"
if [ ! -d "$DATASET_PATH" ]; then
    echo "❌ 数据集路径不存在: $DATASET_PATH"
    echo "请检查数据集路径或修改脚本中的DATASET_PATH变量"
    exit 1
fi

echo "✅ 数据集路径: $DATASET_PATH"

# 训练命令 - 所有关键参数通过命令行显式指定
python train.py \
    --config-name=train_diffusion_unet_hybrid_pointcloud_workspace \
    \
    `# 数据配置 - 与官方完全一致` \
    task.dataset.zarr_path=$DATASET_PATH \
    task.dataset.seed=42 \
    task.dataset.val_ratio=0.0 \
    task.dataset.max_train_episodes=90 \
    \
    `# 训练配置 - 关键：使用官方种子0` \
    training.seed=0 \
    training.device=cuda:0 \
    training.num_epochs=1500 \
    training.lr_scheduler=cosine \
    training.lr_warmup_steps=300 \
    training.gradient_accumulate_every=1 \
    training.use_ema=True \
    training.resume=True \
    training.debug=False \
    \
    `# 优化器配置 - 与官方一致` \
    optimizer.lr=1.0e-4 \
    optimizer.weight_decay=1.0e-6 \
    optimizer.betas=[0.95,0.999] \
    optimizer.eps=1.0e-8 \
    \
    `# 数据加载配置` \
    dataloader.batch_size=64 \
    dataloader.num_workers=8 \
    dataloader.shuffle=True \
    dataloader.pin_memory=True \
    dataloader.persistent_workers=False \
    \
    `# 模型配置 - 扩散模型参数` \
    policy.noise_scheduler.num_train_timesteps=50 \
    policy.noise_scheduler.beta_start=0.0001 \
    policy.noise_scheduler.beta_end=0.02 \
    policy.noise_scheduler.beta_schedule=squaredcos_cap_v2 \
    policy.noise_scheduler.clip_sample=True \
    policy.noise_scheduler.set_alpha_to_one=True \
    policy.noise_scheduler.steps_offset=0 \
    policy.noise_scheduler.prediction_type=sample \
    policy.num_inference_steps=10 \
    \
    `# 网络架构配置` \
    policy.diffusion_step_embed_dim=128 \
    policy.down_dims=[256,512,1024] \
    policy.crop_shape=[80,80] \
    policy.kernel_size=5 \
    policy.n_groups=8 \
    policy.pointnet_type=multi_stage_pointnet \
    policy.point_downsample=True \
    policy.use_pc_color=False \
    policy.pointcloud_encoder_cfg.num_points=4096 \
    policy.pointcloud_encoder_cfg.out_channels=128 \
    policy.pointcloud_encoder_cfg.use_layernorm=True \
    policy.pointcloud_encoder_cfg.final_norm=layernorm \
    policy.pointcloud_encoder_cfg.in_channels=3 \
    policy.pointcloud_encoder_cfg.normal_channel=False \
    \
    `# SE3增强配置` \
    policy.se3_augmentation_cfg.use_aug=False \
    policy.se3_augmentation_cfg.rotation=False \
    policy.se3_augmentation_cfg.rotation_angle=[15,15,15] \
    policy.se3_augmentation_cfg.translation=True \
    policy.se3_augmentation_cfg.translation_scale=0.01 \
    policy.se3_augmentation_cfg.jitter=True \
    policy.se3_augmentation_cfg.jitter_scale=0.01 \
    \
    `# EMA配置` \
    ema.power=0.75 \
    ema.max_value=0.9999 \
    ema.min_value=0.0 \
    ema.inv_gamma=1.0 \
    ema.update_after_step=0 \
    \
    `# 检查点配置` \
    checkpoint.save_ckpt=True \
    checkpoint.topk.monitor_key=test_mean_score \
    checkpoint.topk.mode=max \
    checkpoint.topk.k=5 \
    checkpoint.save_last_ckpt=True \
    checkpoint.save_last_snapshot=False \
    \
    `# 日志配置` \
    exp_name=gr1_dex-3d-hybrid-pointcloud-seed0 \
    logging.mode=offline \
    logging.project=gr1_dex-3d-hybrid-pointcloud \
    logging.group=gr1_dex-3d-hybrid-pointcloud-seed0 \
    logging.name=0 \
    logging.resume=True \
    \
    `# 训练频率配置` \
    training.checkpoint_every=100 \
    training.val_every=100 \
    training.sample_every=5 \
    training.rollout_every=400 \
    training.tqdm_interval_sec=1.0 \
    training.save_video=True

echo "=========================================="
echo "✅ 训练命令已启动"
echo ""
echo "📋 关键配置："
echo "  🔧 Workspace: TrainDiffusionUnetHybridPointcloudWorkspace"
echo "  📄 配置文件: train_diffusion_unet_hybrid_pointcloud_workspace.yaml"
echo "  🎲 训练种子: 0 (与官方一致)"
echo "  📊 数据种子: 42"
echo "  📈 验证集比例: 0.0"
echo "  🎯 最大训练episodes: 90"
echo "  📦 批次大小: 64"
echo "  📉 学习率: 1e-4"
echo "  🔄 训练epochs: 1500"
echo "  💾 保存top-5模型"
echo "  📁 数据集: $DATASET_PATH"
echo ""
echo "🎯 模型架构："
echo "  📊 总参数: ~68.8M (与官方权重兼容)"
echo "  🧠 扩散步数: 50 (训练) / 10 (推理)"
echo "  🔗 下采样维度: [256, 512, 1024]"
echo "  📐 点云编码器: 128维输出, 4096点"
echo "  🎛️ EMA衰减: 0.75"
echo ""
echo "⚡ 硬件配置："
echo "  🖥️ GPU: CUDA:0"
echo "  🧵 数据加载: 8 workers"
echo "  💾 内存: pin_memory=True"
echo ""
echo "📝 训练监控："
echo "  📊 检查点: 每100轮保存"
echo "  📈 验证: 每100轮评估"
echo "  📋 日志: offline模式"
echo "  🎯 项目: gr1_dex-3d-hybrid-pointcloud"
echo "=========================================="
