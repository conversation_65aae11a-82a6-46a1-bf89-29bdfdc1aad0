#!/bin/bash

# 训练脚本 - 完全对齐官方权重配置
# 使用官方的TrainDiffusionUnetHybridPointcloudWorkspace

echo "=========================================="
echo "开始训练 - 官方权重对齐配置"
echo "=========================================="

# 激活conda环境
source ~/miniconda3/etc/profile.d/conda.sh
conda activate idp3

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0
export WANDB_SILENT=True

# 训练命令 - 所有关键参数通过命令行指定
python train.py \
    --config-name=idp3_official_aligned \
    \
    `# 数据配置 - 与官方完全一致` \
    task.dataset.zarr_path=/home/<USER>/code/company/torqueidp3/data/raw_pour_converted \
    task.dataset.seed=42 \
    task.dataset.val_ratio=0.0 \
    task.dataset.max_train_episodes=90 \
    \
    `# 训练配置 - 关键：使用官方种子0` \
    training.seed=0 \
    training.device=cuda:0 \
    training.num_epochs=1500 \
    training.lr_scheduler=cosine \
    training.lr_warmup_steps=300 \
    training.gradient_accumulate_every=1 \
    training.use_ema=True \
    training.resume=True \
    \
    `# 优化器配置 - 与官方一致` \
    optimizer.lr=1.0e-4 \
    optimizer.weight_decay=1.0e-6 \
    optimizer.betas=[0.95,0.999] \
    optimizer.eps=1.0e-8 \
    \
    `# 数据加载配置` \
    dataloader.batch_size=64 \
    dataloader.num_workers=8 \
    dataloader.shuffle=True \
    dataloader.pin_memory=True \
    \
    `# 模型配置 - 扩散模型参数` \
    policy.noise_scheduler.num_train_timesteps=50 \
    policy.noise_scheduler.beta_start=0.0001 \
    policy.noise_scheduler.beta_end=0.02 \
    policy.noise_scheduler.beta_schedule=squaredcos_cap_v2 \
    policy.noise_scheduler.clip_sample=True \
    policy.noise_scheduler.set_alpha_to_one=True \
    policy.noise_scheduler.steps_offset=0 \
    policy.noise_scheduler.prediction_type=sample \
    policy.num_inference_steps=10 \
    \
    `# 网络架构配置` \
    policy.diffusion_step_embed_dim=128 \
    policy.down_dims=[256,512,1024] \
    policy.crop_shape=[80,80] \
    policy.kernel_size=5 \
    policy.n_groups=8 \
    policy.pointnet_type=multi_stage_pointnet \
    policy.pointcloud_encoder_cfg.num_points=4096 \
    policy.pointcloud_encoder_cfg.out_channels=128 \
    policy.pointcloud_encoder_cfg.use_layernorm=True \
    policy.pointcloud_encoder_cfg.final_norm=layernorm \
    \
    `# SE3增强配置` \
    policy.se3_augmentation_cfg.use_aug=False \
    policy.se3_augmentation_cfg.rotation=False \
    policy.se3_augmentation_cfg.translation=True \
    policy.se3_augmentation_cfg.jitter=True \
    \
    `# EMA配置` \
    ema.power=0.75 \
    ema.max_value=0.9999 \
    ema.min_value=0.0 \
    ema.inv_gamma=1.0 \
    ema.update_after_step=0 \
    \
    `# 检查点配置` \
    checkpoint.save_ckpt=True \
    checkpoint.topk.monitor_key=test_mean_score \
    checkpoint.topk.mode=max \
    checkpoint.topk.k=0 \
    checkpoint.save_last_ckpt=True \
    checkpoint.save_last_snapshot=False \
    \
    `# 日志配置` \
    exp_name=gr1_dex-3d-official-aligned-seed0 \
    logging.mode=offline \
    logging.project=gr1_dex-3d-official-aligned-seed0 \
    \
    `# 训练频率配置` \
    training.checkpoint_every=50 \
    training.val_every=50 \
    training.sample_every=5 \
    training.rollout_every=200

echo "=========================================="
echo "训练命令已启动"
echo "关键配置："
echo "  - Workspace: TrainDiffusionUnetHybridPointcloudWorkspace"
echo "  - 训练种子: 0 (与官方一致)"
echo "  - 数据种子: 42"
echo "  - 验证集比例: 0.0"
echo "  - 最大训练episodes: 90"
echo "  - 批次大小: 64 (与官方一致，充分利用GPU)"
echo "  - 学习率: 1e-4"
echo "  - 训练epochs: 1500 (补偿小数据集)"
echo "  - 每epoch: ~35步"
echo "  - 总训练步数: ~52,500 (官方的4.9倍)"
echo "=========================================="
