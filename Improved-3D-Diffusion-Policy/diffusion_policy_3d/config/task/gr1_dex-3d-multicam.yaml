# 与checkpoint完全匹配的任务配置
# 基于 /home/<USER>/code/company/torqueidp3/data/gr1_dex-3d-idp3-pour_seed0/.hydra/config.yaml

name: box  # 与checkpoint完全一致

shape_meta: &shape_meta
  # 与checkpoint完全一致的shape_meta配置
  obs:
    front_image:
      shape: [3, 84, 84]
      type: rgb
    right_image:
      shape: [3, 84, 84]
      type: rgb
    front_point_cloud:
      shape: [10000, 6]
      type: point_cloud
    right_point_cloud:
      shape: [10000, 6]
      type: point_cloud
    agent_pos:
      shape: [32]
      type: low_dim
  action:
    shape: [25]

dataset:
  _target_: diffusion_policy_3d.dataset.gr1_dex_dataset_multicam.GR1DexDatasetMultiCam
  zarr_path: raw_pour_converted  # 将通过命令行参数覆盖
  horizon: ${horizon}
  pad_before: ${eval:'${n_obs_steps}-1'}
  pad_after: ${eval:'${n_action_steps}-1'}
  seed: 42
  val_ratio: 0.0
  max_train_episodes: 90
  use_language: ${use_language}
  use_img: ${use_image}
  use_front_cam: ${policy.pointcloud_encoder_cfg.use_front_cam}
  num_points_front: ${policy.pointcloud_encoder_cfg.num_points_front}
  use_right_cam: ${policy.pointcloud_encoder_cfg.use_right_cam}
  num_points_right: ${policy.pointcloud_encoder_cfg.num_points_right}
