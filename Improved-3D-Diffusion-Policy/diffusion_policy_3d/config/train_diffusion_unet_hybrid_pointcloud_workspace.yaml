# 完全匹配checkpoint配置文件
# 基于 /home/<USER>/code/company/torqueidp3/data/gr1_dex-3d-idp3-pour_seed0/.hydra/config.yaml

defaults:
  - _self_

# 任务配置 - 适配模型期望的键名
task:
  name: box
  shape_meta:
    obs:
      # 模型期望的标准键名
      point_cloud:
        shape: [4096, 6]  # 实际使用的点数
        type: point_cloud
      agent_pos:
        shape: [32]
        type: low_dim
    action:
      shape: [25]
  dataset:
    _target_: diffusion_policy_3d.dataset.gr1_dex_dataset_3d.GR1DexDataset3D  # 使用标准数据集
    zarr_path: /home/<USER>/code/company/torqueidp3/data/raw_pour_converted  # 将被命令行覆盖
    horizon: ${horizon}
    pad_before: ${eval:'${n_obs_steps}-1'}
    pad_after: ${eval:'${n_action_steps}-1'}
    seed: 42
    val_ratio: 0.0
    max_train_episodes: 90
    num_points: 4096

name: train_diffusion_unet_hybrid
_target_: diffusion_policy_3d.workspace.train_diffusion_unet_hybrid_pointcloud_workspace.TrainDiffusionUnetHybridPointcloudWorkspace

task_name: ${task.name}
shape_meta: ${task.shape_meta}
exp_name: gr1_dex-3d-idp3-pour3

# 与checkpoint完全一致的配置
n_obs_steps: 2
horizon: 16
n_action_steps: 15
n_latency_steps: 0
dataset_obs_steps: ${n_obs_steps}
keypoint_visible_rate: 1.0
obs_as_global_cond: true
use_language: false
use_image: false

policy:
  _target_: diffusion_policy_3d.policy.diffusion_pointcloud_policy.DiffusionPointcloudPolicy
  use_point_crop: true
  use_down_condition: true
  use_mid_condition: true
  use_up_condition: true
  use_image: false
  diffusion_step_embed_dim: 128
  down_dims: [256, 512, 1024]
  crop_shape: [80, 80]
  horizon: ${horizon}
  kernel_size: 5
  n_action_steps: ${n_action_steps}
  n_groups: 8
  n_obs_steps: ${n_obs_steps}
  use_language: ${use_language}

  noise_scheduler:
    _target_: diffusers.schedulers.scheduling_ddim.DDIMScheduler
    num_train_timesteps: 50
    beta_start: 0.0001
    beta_end: 0.02
    beta_schedule: squaredcos_cap_v2
    clip_sample: true
    set_alpha_to_one: true
    steps_offset: 0
    prediction_type: sample

  num_inference_steps: 10
  obs_as_global_cond: true
  shape_meta: ${shape_meta}
  use_pc_color: false
  pointnet_type: multi_stage_pointnet
  point_downsample: true

  se3_augmentation_cfg:
    use_aug: false
    rotation: false
    rotation_angle: [15, 15, 15]
    translation: true
    translation_scale: 0.01
    jitter: true
    jitter_scale: 0.01

  pointcloud_encoder_cfg:
    in_channels: 3
    out_channels: 128
    use_layernorm: true
    final_norm: layernorm
    normal_channel: false
    num_points: 4096

# 完全匹配官方EMA配置
ema:
  _target_: diffusion_policy_3d.model.diffusion.ema_model.EMAModel
  update_after_step: 0
  inv_gamma: 1.0
  power: 0.75
  min_value: 0.0
  max_value: 0.9999

# 完全匹配官方数据加载配置
dataloader:
  batch_size: 64
  num_workers: 8
  shuffle: true
  pin_memory: true
  persistent_workers: false

val_dataloader:
  batch_size: 64
  num_workers: 8
  shuffle: false
  pin_memory: true
  persistent_workers: false

# 与checkpoint完全一致的优化器配置
optimizer:
  _target_: torch.optim.AdamW
  lr: 0.0001  # 与checkpoint完全一致
  betas: [0.95, 0.999]
  eps: 1.0e-08  # 与checkpoint完全一致
  weight_decay: 1.0e-06  # 与checkpoint完全一致

# 与checkpoint完全一致的训练配置
training:
  device: "cuda:0"
  seed: 0  # 与checkpoint完全一致
  debug: false  # 与checkpoint完全一致
  resume: true  # 与checkpoint完全一致
  lr_scheduler: cosine
  lr_warmup_steps: 500  # 与checkpoint完全一致
  num_epochs: 301  # 与checkpoint完全一致
  gradient_accumulate_every: 1
  use_ema: true  # 与checkpoint完全一致
  rollout_every: 400
  checkpoint_every: 100
  val_every: 100
  sample_every: 5
  max_train_steps: null
  max_val_steps: null
  tqdm_interval_sec: 1.0
  save_video: true  # 与checkpoint完全一致

logging:
  group: ${exp_name}
  id: null
  mode: offline  # 与checkpoint完全一致
  name: ${training.seed}
  project: humanoid_mimic  # 与checkpoint完全一致
  resume: true
  tags:
  - train_diffusion_unet_hybrid
  - dexdeform

checkpoint:
  save_ckpt: true  # 与checkpoint完全一致
  topk:
    monitor_key: test_mean_score
    mode: max
    k: 0  # 与checkpoint完全一致
    format_str: 'epoch={epoch:04d}-test_mean_score={test_mean_score:.3f}.ckpt'  # 与checkpoint完全一致
  save_last_ckpt: true  # 与checkpoint完全一致
  save_last_snapshot: false  # 与checkpoint完全一致

multi_run:
  run_dir: data/outputs/${now:%Y.%m.%d}/${now:%H.%M.%S}_${name}_${task_name}
  wandb_name_base: ${now:%Y.%m.%d-%H.%M.%S}_${name}_${task_name}

hydra:
  job:
    override_dirname: ${name}
  run:
    dir: data/outputs/${now:%Y.%m.%d}/${now:%H.%M.%S}_${name}_${task_name}
  sweep:
    dir: data/outputs/${now:%Y.%m.%d}/${now:%H.%M.%S}_${name}_${task_name}
    subdir: ${hydra.job.num}
