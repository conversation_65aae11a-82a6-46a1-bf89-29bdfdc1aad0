#!/bin/bash

# 完全匹配checkpoint的训练脚本
echo "==========================================="
echo "🚀 开始训练 - 完全匹配checkpoint配置"
echo "==========================================="

# 激活conda环境
source ~/miniconda3/etc/profile.d/conda.sh
conda activate idp3

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0
export WANDB_SILENT=True
export HYDRA_FULL_ERROR=1

# 数据路径配置
DATASET_PATH="/home/<USER>/code/company/torqueidp3/data/raw_pour_converted"
if [ ! -d "$DATASET_PATH" ]; then
    echo "❌ 数据集路径不存在: $DATASET_PATH"
    exit 1
fi

echo "✅ 数据集路径: $DATASET_PATH"
echo "✅ 配置文件: train_diffusion_unet_hybrid_pointcloud_workspace.yaml"
echo "✅ 完全匹配checkpoint参数"

# 训练命令 - 使用YAML文件中的所有配置，只覆盖数据路径
python train.py \
    --config-name=train_diffusion_unet_hybrid_pointcloud_workspace \
    task.dataset.zarr_path=$DATASET_PATH

echo "✅ 训练启动完成"
