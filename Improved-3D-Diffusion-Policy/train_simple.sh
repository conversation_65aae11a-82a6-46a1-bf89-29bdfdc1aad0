#!/bin/bash

# 简化版训练脚本 - 用于测试基本配置
echo "🚀 开始简化训练测试"

# 激活conda环境
source ~/miniconda3/etc/profile.d/conda.sh
conda activate idp3

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0
export WANDB_SILENT=True
export HYDRA_FULL_ERROR=1

# 数据路径配置
DATASET_PATH="/home/<USER>/code/company/torqueidp3/data/raw_pour_converted"
if [ ! -d "$DATASET_PATH" ]; then
    echo "❌ 数据集路径不存在: $DATASET_PATH"
    exit 1
fi

echo "✅ 数据集路径: $DATASET_PATH"

# 简化的训练命令 - 只设置最基本的参数
python train.py \
    --config-name=train_diffusion_unet_hybrid_pointcloud_workspace \
    task.dataset.zarr_path=$DATASET_PATH \
    training.seed=0 \
    training.device=cuda:0 \
    training.num_epochs=5 \
    training.debug=false \
    exp_name=test-simple \
    logging.mode=offline

echo "✅ 简化训练测试完成"
